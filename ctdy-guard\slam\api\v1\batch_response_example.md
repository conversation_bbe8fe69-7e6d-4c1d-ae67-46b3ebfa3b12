# 批量评估和审核API响应示例

## API概述

本文档详细描述了评估和审核系统中批量操作API的响应格式和示例。系统提供以下主要接口：

- `POST /api/v1/evaluate` - 批量评估接口
- `POST /api/v1/review` - 批量审核接口  
- `GET /api/v1/evaluate` - 获取评估详情
- `GET /api/v1/evaluator` - 获取评估人列表

## 修改说明

`HandlePostEvaluate` 和 `HandlePostReview` 函数现在支持返回详细的批量操作结果，包括：
- 成功和失败的数量统计
- 成功处理的ID列表
- 失败项的详细错误信息
- 详细的错误原因和上下文

## 请求结构

```go
type EvaluateBody struct {
    XflowIds   []string `json:"ids"`         // 流程ID列表，必填
    Opinion    string   `json:"opinion"`     // 评估意见，如："approved", "rejected", "pending"
    Msg        string   `json:"msg"`         // 评估消息
    CommentMsg string   `json:"comment_msg"` // 评论消息
}
```

## 响应结构

```go
type BatchResult struct {
    SuccessCount int               `json:"success_count"` // 成功数量
    FailCount    int               `json:"fail_count"`    // 失败数量
    TotalCount   int               `json:"total_count"`   // 总数量
    SuccessIds   []string          `json:"success_ids"`   // 成功的ID列表
    FailedItems  []BatchFailedItem `json:"failed_items"`  // 失败项详情
}

type BatchFailedItem struct {
    XflowId string `json:"xflow_id"` // 失败的流程ID
    Error   string `json:"error"`    // 错误信息
}
```

## 通用响应格式

所有API响应都遵循统一的格式：

```go
type Response struct {
    Code    int         `json:"code"`    // 状态码
    Msg     string      `json:"msg"`     // 响应消息
    Data    interface{} `json:"data"`    // 响应数据
    TraceId string      `json:"traceid"` // 追踪ID
}
```

## 响应示例

### 1. 全部成功的情况

**请求:**
```json
{
    "ids": ["123", "456", "789"],
    "opinion": "approved",
    "msg": "评估通过",
    "comment_msg": "质量良好"
}
```

**响应:**
```json
{
    "code": 200,
    "msg": "评估成功",
    "data": {
        "success_count": 3,
        "fail_count": 0,
        "total_count": 3,
        "success_ids": ["123", "456", "789"],
        "failed_items": []
    },
    "traceid": "trace-123"
}
```

### 2. 部分成功的情况

**请求:**
```json
{
    "ids": ["123", "456", "invalid_id", "0"],
    "opinion": "approved",
    "msg": "评估通过",
    "comment_msg": "质量良好"
}
```

**响应:**
```json
{
    "code": 200,
    "msg": "评估完成，成功：2条，失败：2条",
    "data": {
        "success_count": 2,
        "fail_count": 2,
        "total_count": 4,
        "success_ids": ["123", "456"],
        "failed_items": [
            {
                "xflow_id": "invalid_id",
                "error": "invalid xflow_id: invalid_id"
            },
            {
                "xflow_id": "0",
                "error": "invalid xflow_id: 0, must be positive"
            }
        ]
    },
    "traceid": "trace-456"
}
```

### 3. 全部失败的情况

**请求:**
```json
{
    "ids": ["invalid", "999"],
    "opinion": "approved",
    "msg": "评估通过",
    "comment_msg": "质量良好"
}
```

**响应:**
```json
{
    "code": 422,
    "msg": "所有评估都失败了",
    "data": {
        "success_count": 0,
        "fail_count": 2,
        "total_count": 2,
        "success_ids": [],
        "failed_items": [
            {
                "xflow_id": "invalid",
                "error": "invalid xflow_id: invalid"
            },
            {
                "xflow_id": "999",
                "error": "evaluate list not found for xflow_id and userId: 999 1001"
            }
        ]
    },
    "traceid": "trace-789"
}
```

## 4. 请求参数验证失败示例

### 4.1 空ID列表

**请求:**
```json
{
    "ids": [],
    "opinion": "approved",
    "msg": "评估通过",
    "comment_msg": "质量良好"
}
```

**响应:**
```json
{
    "code": 422,
    "msg": "评估ID不能为空",
    "data": null,
    "traceid": "trace-1001"
}
```

### 4.2 JSON格式错误

**请求:**
```json
{
    "ids": ["123"],
    "opinion": "approved"
    "msg": "评估通过"  // 缺少逗号
}
```

**响应:**
```json
{
    "code": 422,
    "msg": "评估信息输入异常",
    "data": null,
    "traceid": "trace-1002"
}
```

## 5. 用户权限验证失败示例

### 5.1 审核权限不足（仅限审核接口）

**请求:**
```json
{
    "ids": ["123"],
    "opinion": "approved",
    "msg": "审核通过",
    "comment_msg": "质量良好"
}
```

**响应:**
```json
{
    "code": 422,
    "msg": "用户没有评审权限",
    "data": null,
    "traceid": "trace-1003"
}
```

### 5.2 用户信息获取失败

**响应:**
```json
{
    "code": 422,
    "msg": "从Context获取用户信息失败!",
    "data": null,
    "traceid": "trace-1004"
}
```

## 6. 批量操作性能指标

## 7. 错误代码说明

| 错误代码 | 错误描述                         | 说明                          |
|----------|----------------------------------|-------------------------------|
| 400      | BAD_REQUEST                      | 请求参数验证失败              |
| 401      | UNAUTHORIZED                     | 未授权访问                    |
| 403      | FORBIDDEN                        | 权限不足                      |
| 422      | UNPROCESSABLE_ENTITY             | 业务逻辑验证失败              |
| 500      | INTERNAL_SERVER_ERROR            | 服务器内部错误                |

## 8. 评估与审核流程差异

### 8.1 评估流程（HandlePostEvaluate）

**处理逻辑：**
1. 验证用户身份（无需特殊权限）
2. 根据xflow_id和user_id查找现有评估记录
3. 更新现有记录的Opinion、Msg、CommentMsg字段
4. 如果记录不存在，返回错误

**关键函数调用：**
- `model.GetEvaluateListByUserXflowId(xflowId, *userId)`
- `model.UpdateEvaluateList(&els[i])`

### 8.2 审核流程（HandlePostReview）

**处理逻辑：**
1. 验证用户身份和管理员权限
2. 调用`model.AdministratorCheck(*user.Id)`检查权限
3. 根据xflow_id和user_id查找现有审核记录
4. 如果记录存在，更新记录
5. 如果记录不存在，创建新的审核记录（evaluate_type = EVALUATE_TYPE_REVIEW）

**关键函数调用：**
- `model.AdministratorCheck(*user.Id)`
- `model.GetReviewListByUserXflowId(xflowId, *userId)`
- `model.CreateEvaluateList(&evaluateItem)` 或 `model.UpdateEvaluateList(el)`

### 8.3 权限差异

| 操作类型 | 权限要求 | 检查方式 |
|----------|----------|----------|
| 评估 | 普通用户 | 仅需有效用户身份 |
| 审核 | 管理员 | `AdministratorCheck()` |

## 9. 其他API接口示例

### 9.1 获取评估详情 (GET /api/v1/evaluate)

**请求参数:**
- `id`: 流程ID (query参数)

**请求示例:**
```
GET /api/v1/evaluate?id=123
```

**成功响应:**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "evaluate_type": 1,
            "xflow_id": 123,
            "user_id": 1001,
            "opinion": "approved",
            "msg": "评估通过",
            "comment_msg": "质量良好",
            "created_at": "2025-08-07T14:27:06Z",
            "updated_at": "2025-08-07T14:27:06Z"
        }
    ],
    "traceid": "trace-eval-001"
}
```

**失败响应:**
```json
{
    "code": 422,
    "msg": "查询参数异常",
    "data": null,
    "traceid": "trace-eval-002"
}
```

### 9.2 获取评估人列表 (GET /api/v1/evaluator)

**请求参数:**
- `key`: 搜索关键字 (query参数，可选)

**请求示例:**
```
GET /api/v1/evaluator?key=张三
```

**成功响应:**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "id": 1001,
            "name": "张三",
            "email": "[email]",
            "department": "技术部",
            "role": "高级工程师"
        },
        {
            "id": 1002,
            "name": "张三丰",
            "email": "[email]",
            "department": "产品部",
            "role": "产品经理"
        }
    ],
    "traceid": "trace-evaluator-001"
}
```

## 10. 日志记录示例

## 11. 错误处理机制

### 11.1 常见错误类型

## 12. 推荐的测试用例

### 12.1 基本功能测试

**评估接口测试:**
```bash
# 单个ID测试
curl -X POST /api/v1/evaluate \
  -H "Content-Type: application/json" \
  -d '{"ids":["123"],"opinion":"approved","msg":"测试评估","comment_msg":"测试评论"}'

# 多个ID测试
curl -X POST /api/v1/evaluate \
  -H "Content-Type: application/json" \
  -d '{"ids":["123","456","789"],"opinion":"approved","msg":"批量评估","comment_msg":"批量评论"}'
```

**审核接口测试:**
```bash
# 管理员审核测试
curl -X POST /api/v1/review \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin_token" \
  -d '{"ids":["123"],"opinion":"approved","msg":"审核通过","comment_msg":"审核评论"}'
```

### 12.2 边界条件测试

1. **ID边界值测试**
   - 最小正整数: `"1"`
   - 最大整数: `"2147483647"`
   - 零值: `"0"` (应该失败)
   - 负数: `"-1"` (应该失败)
   - 非数字: `"abc"` (应该失败)

2. **数组边界测试**
   - 空数组: `[]` (应该失败)
   - 单元素: `["123"]`
   - 大量元素: 1000个ID的数组

### 12.3 性能测试

### 12.4 安全测试

1. **权限测试**
   - 普通用户调用审核接口 (应该失败)
   - 无效token访问 (应该失败)
   - 过期token访问 (应该失败)

2. **输入验证测试**
   - SQL注入: `{"ids":["1'; DROP TABLE users; --"]}`
   - XSS攻击: `{"msg":"<script>alert('xss')</script>"}`
   - 超长字符串: 10KB的msg字段

3. **并发测试**
   - 同时100个请求
   - 相同ID的并发更新
   - 不同用户同时操作相同流程
## 1
3. 数据模型详解

### 13.1 EvaluateList模型字段

EvaluateList模型包含以下关键字段：

```go
type EvaluateList struct {
    ID            int    `json:"id"`
    Evaluate_type int    `json:"evaluate_type"` // 评估类型：1=评估，2=审核(EVALUATE_TYPE_REVIEW)
    Xflow_id      int    `json:"xflow_id"`      // 流程ID
    User_id       int    `json:"user_id"`       // 用户ID
    Opinion       string `json:"opinion"`       // 评估意见
    Msg           string `json:"msg"`           // 评估消息
    CommentMsg    string `json:"comment_msg"`   // 评论消息
    CreatedAt     string `json:"created_at"`    // 创建时间
    UpdatedAt     string `json:"updated_at"`    // 更新时间
}
```

### 13.2 评估类型常量

```go
const (
    EVALUATE_TYPE_EVALUATE = 1  // 评估类型
    EVALUATE_TYPE_REVIEW   = 2  // 审核类型
)
```

## 14. 核心业务逻辑流程图

### 14.1 评估流程

```
开始 → 参数验证 → 获取用户信息 → 遍历ID列表 → 
  ↓
对每个ID:
  转换为整数 → 验证正数 → 查询现有记录 → 更新记录 → 
  ↓
记录结果(成功/失败) → 下一个ID
  ↓
所有ID处理完成 → 生成批量结果 → 返回响应
```

### 14.2 审核流程

```
开始 → 参数验证 → 获取用户信息 → 权限检查 → 遍历ID列表 →
  ↓
对每个ID:
  转换为整数 → 验证正数 → 查询现有审核记录 →
  ↓
  存在记录? → 是: 更新记录
      ↓
      否: 创建新记录(evaluate_type=REVIEW)
  ↓
记录结果(成功/失败) → 下一个ID
  ↓
所有ID处理完成 → 生成批量结果 → 返回响应
```

## 15. 数据库操作说明

### 15.1 关键数据库函数

| 函数名 | 用途 | 返回值 |
|--------|------|--------|
| `GetEvaluateInfoByXflowId(id)` | 根据流程ID获取评估详情 | 评估信息列表 |
| `GetEvaluateListByUserXflowId(xflowId, userId)` | 获取用户的评估记录 | 评估记录列表 |
| `GetReviewListByUserXflowId(xflowId, userId)` | 获取用户的审核记录 | 单个审核记录 |
| `UpdateEvaluateList(item)` | 更新评估记录 | bool |
| `CreateEvaluateList(item)` | 创建评估记录 | bool |
| `AdministratorCheck(userId)` | 检查管理员权限 | bool |
| `GetEvaluaterList(key)` | 获取评估人列表 | 评估人列表 |

### 15.2 数据一致性保证

- **评估操作**: 只更新现有记录，不创建新记录
- **审核操作**: 可以创建新记录或更新现有记录
- **权限控制**: 审核操作需要管理员权限验证
- **用户关联**: 所有操作都与当前登录用户关联
